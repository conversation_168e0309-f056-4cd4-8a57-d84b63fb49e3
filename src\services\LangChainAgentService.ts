import { Chat<PERSON><PERSON>AI } from '@langchain/openai';
import { AgentExecutor, createOpenAIFunctionsAgent } from 'langchain/agents';
import { ChatPromptTemplate, MessagesPlaceholder } from '@langchain/core/prompts';
import { AIMessage, HumanMessage, BaseMessage } from '@langchain/core/messages';
import { DynamicTool } from '@langchain/core/tools';
import { BufferMemory } from 'langchain/memory';
import { AdvancedAgentTools } from './AdvancedAgentTools';
import { VectorStoreService } from './VectorStoreService';
import { AgentTool, ConversationContext, APIResponse } from './MicrochipAPI';

export interface AgentExecutionResult {
  output: string;
  intermediateSteps: Array<{
    action: string;
    observation: string;
  }>;
  reasoning: string;
  toolsUsed: string[];
}

export class LangChainAgentService {
  private llm: ChatOpenAI;
  private agentExecutor: AgentExecutor | null = null;
  private memory: BufferMemory;
  private advancedTools: AdvancedAgentTools;
  private vectorStore: VectorStoreService;
  private conversationContext: ConversationContext;
  private isInitialized: boolean = false;

  constructor(
    openaiApiKey: string,
    workspaceRoot: string,
    conversationContext: ConversationContext
  ) {
    this.llm = new ChatOpenAI({
      openAIApiKey: openaiApiKey,
      modelName: 'gpt-4',
      temperature: 0.1,
      maxTokens: 2000
    });

    this.memory = new BufferMemory({
      memoryKey: 'chat_history',
      returnMessages: true,
      inputKey: 'input',
      outputKey: 'output'
    });

    this.advancedTools = new AdvancedAgentTools(workspaceRoot);
    this.vectorStore = new VectorStoreService(workspaceRoot, openaiApiKey);
    this.conversationContext = conversationContext;
  }

  async initialize(): Promise<void> {
    try {
      // Initialize vector store
      await this.vectorStore.initialize();

      // Create tools
      const tools = this.createLangChainTools();

      // Create the agent prompt
      const prompt = this.createAgentPrompt();

      // Create the agent
      const agent = await createOpenAIFunctionsAgent({
        llm: this.llm,
        tools,
        prompt
      });

      // Create agent executor
      this.agentExecutor = new AgentExecutor({
        agent,
        tools,
        memory: this.memory,
        verbose: true,
        maxIterations: 10,
        returnIntermediateSteps: true
      });

      this.isInitialized = true;
      console.log('LangChain Agent Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize LangChain Agent Service:', error);
      throw error;
    }
  }

  async executeQuery(query: string): Promise<AgentExecutionResult> {
    if (!this.isInitialized || !this.agentExecutor) {
      throw new Error('Agent service not initialized');
    }

    try {
      // Update context with current workspace state
      await this.updateWorkspaceContext();

      // Execute the agent
      const result = await this.agentExecutor.invoke({
        input: query,
        chat_history: await this.memory.chatHistory.getMessages()
      });

      // Extract intermediate steps and reasoning
      const intermediateSteps = result.intermediateSteps || [];
      const toolsUsed: string[] = intermediateSteps.map((step: any) => step.action?.tool || 'unknown');
      
      const reasoning = this.extractReasoning(intermediateSteps);

      return {
        output: result.output,
        intermediateSteps: intermediateSteps.map((step: any) => ({
          action: step.action?.tool || 'unknown',
          observation: step.observation || ''
        })),
        reasoning,
        toolsUsed: [...new Set(toolsUsed)]
      };
    } catch (error) {
      console.error('Agent execution failed:', error);
      throw error;
    }
  }

  async processWithAdvancedAgent(userMessage: string): Promise<APIResponse> {
    try {
      const result = await this.executeQuery(userMessage);

      return {
        success: true,
        message: result.output,
        intent: 'advanced-agent',
        toolUsed: result.toolsUsed.join(', '),
        confidence: 0.9,
        reasoning: result.reasoning,
        toolExecutionSteps: result.intermediateSteps.map(step => 
          `${step.action}: ${step.observation.substring(0, 100)}...`
        )
      };
    } catch (error) {
      return {
        success: false,
        message: 'Agent execution failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        intent: 'error',
        toolUsed: 'none',
        confidence: 0
      };
    }
  }

  private createLangChainTools(): DynamicTool[] {
    const agentTools = [
      ...this.advancedTools.getAllTools(),
      this.vectorStore.createSemanticSearchTool()
    ];

    return agentTools.map(tool => new DynamicTool({
      name: tool.name,
      description: tool.description,
      func: async (input: string) => {
        try {
          return await tool.execute(input, this.conversationContext);
        } catch (error) {
          return `Error executing ${tool.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
      }
    }));
  }

  private createAgentPrompt(): ChatPromptTemplate {
    const systemMessage = `You are a powerful AI code assistant with full access to a user's codebase and development environment.

CAPABILITIES:
- Read, write, create, and move files in the workspace
- Search code semantically and syntactically across the entire codebase
- Analyze code structure, functions, classes, and dependencies
- Get context about the currently active file and user selections
- Refactor code and suggest improvements
- Help with debugging and troubleshooting
- Automatically generate complete programs and create files

APPROACH:
1. Always understand the user's request completely before taking action
2. Use GetActiveFileContext to understand the current development context
3. Use semantic search to find relevant code when working with existing codebases
4. Read files before modifying them to understand the current state
5. Be proactive in suggesting improvements and best practices
6. Explain your reasoning and what you're doing at each step
7. When making changes, always verify the results
8. For specific program requests (like prime numbers), automatically create complete, working programs

TOOLS AVAILABLE:
- ReadFile: Read file contents
- WriteFile: Write/update file contents
- CreateFile: Create new files
- MoveFile: Move/rename files
- ListFiles: List files in workspace
- SearchCode: Search for text patterns in code
- SemanticCodeSearch: Find semantically similar code
- GetActiveFileContext: Get current editor state
- GeneratePrimeNumberProgram: Automatically create complete prime number programs

SPECIAL BEHAVIORS:
- When user asks for "prime number program", "prime numbers", "sieve", or similar requests, AUTOMATICALLY use GeneratePrimeNumberProgram tool
- Always create complete, working programs with documentation and examples
- Automatically open created files in the editor when possible
- Provide comprehensive functionality beyond basic requirements

GUIDELINES:
- Always explain what you're doing before making changes
- Be careful with file operations - ask for confirmation for destructive actions
- Provide code examples and explanations
- Consider the broader codebase context when making suggestions
- Help the user understand the implications of changes
- For program generation requests, be proactive and create complete solutions

Current workspace context:
- Workspace root: {workspace_root}
- Active file: {active_file}
- Recent files: {recent_files}

Remember: You are an expert developer assistant. Be thorough, careful, and helpful. When users request specific programs, automatically create them!`;

    return ChatPromptTemplate.fromMessages([
      ['system', systemMessage],
      new MessagesPlaceholder('chat_history'),
      ['human', '{input}'],
      new MessagesPlaceholder('agent_scratchpad')
    ]);
  }

  private async updateWorkspaceContext(): Promise<void> {
    // Update conversation context with current workspace state
    this.conversationContext.workspaceRoot = this.advancedTools['workspaceRoot'];
    
    // Try to get active file context if in VSCode
    try {
      const activeFileTool = this.advancedTools.createGetActiveFileContextTool();
      const contextResult = await activeFileTool.execute('', this.conversationContext);
      // Context is updated in the tool execution
    } catch (error) {
      // Not in VSCode environment, skip active file context
    }
  }

  private extractReasoning(intermediateSteps: any[]): string {
    if (intermediateSteps.length === 0) {
      return 'Direct response without tool usage';
    }

    const reasoning = intermediateSteps.map((step, index) => {
      const action = step.action?.tool || 'unknown';
      const input = step.action?.toolInput || '';
      return `Step ${index + 1}: Used ${action} with input "${input}"`;
    }).join('\n');

    return reasoning;
  }

  async indexWorkspace(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Agent service not initialized');
    }

    try {
      await this.vectorStore.indexCodebase();
      console.log('Workspace indexed successfully for semantic search');
    } catch (error) {
      console.warn('Failed to index workspace:', error);
    }
  }

  async clearMemory(): Promise<void> {
    await this.memory.clear();
    this.conversationContext.history = [];
    this.conversationContext.sessionData = {};
  }

  getMemory(): BufferMemory {
    return this.memory;
  }

  isReady(): boolean {
    return this.isInitialized;
  }

  async cleanup(): Promise<void> {
    await this.vectorStore.cleanup();
    this.isInitialized = false;
  }
}
