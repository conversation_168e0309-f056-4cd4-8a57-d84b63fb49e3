// Webview-safe version of MicrochipAPI that doesn't import VSCode modules

export interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  intent?: string;
  toolUsed?: string;
  confidence?: number;
  reasoning?: string;
  toolExecutionSteps?: string[];
}

export interface APIResponse {
  success: boolean;
  message: string;
  error?: string;
  intent?: string;
  toolUsed?: string;
  confidence?: number;
  reasoning?: string;
  toolExecutionSteps?: string[];
}

export interface ConversationContext {
  history: ChatMessage[];
  userPreferences: Record<string, unknown>;
  sessionData: Record<string, unknown>;
  activeFile?: string;
  selectedText?: string;
  cursorPosition?: { line: number; character: number };
  workspaceRoot?: string;
}

export class WebviewMicrochipAPI {
  private apiKey: string = '';
  private baseUrl: string = 'https://api.microchip.ai';
  private conversationHistory: ChatMessage[] = [];

  constructor(apiKey?: string) {
    if (apiKey) {
      this.apiKey = apiKey;
    }
  }

  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  getApiKey(): string {
    return this.apiKey;
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const response = await this.makeRequest('/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({ test: true })
      });

      return response.success === true;
    } catch (error) {
      console.error('API key validation failed:', error);
      return false;
    }
  }

  async sendMessage(message: string, context?: ConversationContext): Promise<APIResponse> {
    if (!this.apiKey) {
      return {
        success: false,
        message: '',
        error: 'API key not set'
      };
    }

    try {
      const requestBody = {
        message,
        model: 'microchip-chatbot-internal',
        context: context || this.buildContext(),
        history: this.conversationHistory.slice(-10) // Last 10 messages for context
      };

      const response = await this.makeRequest('/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      // Add messages to conversation history
      const userMessage: ChatMessage = {
        id: this.generateId(),
        text: message,
        isBot: false,
        timestamp: new Date()
      };

      const botMessage: ChatMessage = {
        id: this.generateId(),
        text: response.message || 'No response received',
        isBot: true,
        timestamp: new Date(),
        intent: response.intent,
        toolUsed: response.toolUsed,
        confidence: response.confidence,
        reasoning: response.reasoning,
        toolExecutionSteps: response.toolExecutionSteps
      };

      this.conversationHistory.push(userMessage, botMessage);

      return response;
    } catch (error) {
      console.error('Error sending message:', error);
      return {
        success: false,
        message: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private async makeRequest(endpoint: string, options: {
    method: string;
    headers: Record<string, string>;
    body?: string;
  }): Promise<any> {
    const url = new URL(endpoint, this.baseUrl);

    const fetchOptions: RequestInit = {
      method: options.method,
      headers: options.headers,
      body: options.body
    };

    try {
      const response = await fetch(url.toString(), fetchOptions);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network request failed');
    }
  }

  private buildContext(): ConversationContext {
    return {
      history: this.conversationHistory,
      userPreferences: {},
      sessionData: {},
      workspaceRoot: undefined
    };
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  getConversationHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }

  clearConversationHistory(): void {
    this.conversationHistory = [];
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/health', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });
      return response.status === 'ok';
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }
}

// Export a singleton instance for use in webview
export const webviewMicrochipAPI = new WebviewMicrochipAPI();
