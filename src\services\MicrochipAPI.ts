import { LangChainAgentService } from './LangChainAgentService';
import { AdvancedAgentTools } from './AdvancedAgentTools';
import { VSCodeContextService } from './VSCodeContextService';
import { VectorStoreService } from './VectorStoreService';
import * as https from 'https';
import * as http from 'http';

export interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  intent?: string;
  toolUsed?: string;
  confidence?: number;
  reasoning?: string;
  toolExecutionSteps?: string[];
}

export interface APIResponse {
  success: boolean;
  message: string;
  error?: string;
  intent?: string;
  toolUsed?: string;
  confidence?: number;
  reasoning?: string;
  toolExecutionSteps?: string[];
}

export interface AgentTool {
  name: string;
  description: string;
  keywords: string[];
  execute: (query: string, context: ConversationContext) => Promise<string>;
}

export interface ConversationContext {
  history: ChatMessage[];
  userPreferences: Record<string, unknown>;
  sessionData: Record<string, unknown>;
  activeFile?: string;
  selectedText?: string;
  cursorPosition?: { line: number; character: number };
  workspaceRoot?: string;
}

export interface IntentClassification {
  intent: string;
  confidence: number;
  suggestedTool: string;
  reasoning: string;
}

export interface FileSystemContext {
  workspaceRoot: string;
  activeFile?: string;
  recentFiles: string[];
  projectStructure?: Record<string, any>;
}

export interface CodeAnalysisResult {
  filePath: string;
  content: string;
  language: string;
  functions: string[];
  classes: string[];
  imports: string[];
  issues: string[];
}

export class MicrochipAPI {
  private apiKey: string = '';
  private conversationHistory: string[] = [];
  private readonly baseURL = 'http://localhost:3001';
  private readonly endpoint = '/api/chat';
  private agentMode: boolean = false;
  private advancedAgentMode: boolean = false;
  private conversationContext: ConversationContext;
  private availableTools: Map<string, AgentTool> = new Map();

  // Advanced agent services
  private langChainAgent: LangChainAgentService | null = null;
  private advancedTools: AdvancedAgentTools | null = null;
  private vscodeContext: VSCodeContextService | null = null;
  private vectorStore: VectorStoreService | null = null;
  private openaiApiKey: string = '';

  constructor() {
    this.conversationContext = {
      history: [],
      userPreferences: {},
      sessionData: {}
    };
    this.initializeTools();
    this.initializeAdvancedServices();
  }

  private makeHttpRequest(url: string, options: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const httpModule = isHttps ? https : http;

      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: options.headers || {}
      };

      const req = httpModule.request(requestOptions, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          const response = {
            ok: res.statusCode! >= 200 && res.statusCode! < 300,
            status: res.statusCode,
            statusText: res.statusMessage,
            json: () => Promise.resolve(JSON.parse(data)),
            text: () => Promise.resolve(data)
          };
          resolve(response);
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (options.signal) {
        options.signal.addEventListener('abort', () => {
          req.destroy();
          reject(new Error('Request aborted'));
        });
      }

      if (options.body) {
        req.write(options.body);
      }

      req.end();
    });
  }

  setApiKey(apiKey: string): void {
    this.apiKey = apiKey.trim();
  }

  setOpenAIApiKey(apiKey: string): void {
    this.openaiApiKey = apiKey.trim();
    this.initializeAdvancedServices();
  }

  setAgentMode(enabled: boolean): void {
    this.agentMode = enabled;
  }

  setAdvancedAgentMode(enabled: boolean): void {
    this.advancedAgentMode = enabled;
    if (enabled && !this.langChainAgent) {
      this.initializeAdvancedServices();
    }
  }

  getAgentMode(): boolean {
    return this.agentMode;
  }

  getAdvancedAgentMode(): boolean {
    return this.advancedAgentMode;
  }

  getAvailableTools(): AgentTool[] {
    return Array.from(this.availableTools.values());
  }

  getConversationContext(): ConversationContext {
    return this.conversationContext;
  }

  // Test method to verify agent functionality
  testAgentClassification(query: string): IntentClassification {
    return this.classifyIntent(query);
  }

  getApiKey(): string {
    return this.apiKey;
  }

  clearHistory(): void {
    this.conversationHistory = [];
    this.conversationContext.history = [];
    this.conversationContext.sessionData = {};

    if (this.langChainAgent) {
      this.langChainAgent.clearMemory();
    }
  }

  private async initializeAdvancedServices(): Promise<void> {
    try {
      if (!this.openaiApiKey) {
        console.warn('OpenAI API key not set, advanced agent features will be limited');
        return;
      }

      // Get workspace root
      const workspaceRoot = this.conversationContext.workspaceRoot || process.cwd();

      // Initialize services
      this.advancedTools = new AdvancedAgentTools(workspaceRoot);
      this.vscodeContext = new VSCodeContextService();
      this.vectorStore = new VectorStoreService(workspaceRoot, this.openaiApiKey);

      // Initialize LangChain agent
      this.langChainAgent = new LangChainAgentService(
        this.openaiApiKey,
        workspaceRoot,
        this.conversationContext
      );

      await this.langChainAgent.initialize();

      console.log('Advanced agent services initialized successfully');
    } catch (error) {
      console.error('Failed to initialize advanced agent services:', error);
    }
  }

  async indexWorkspace(): Promise<void> {
    if (this.langChainAgent) {
      await this.langChainAgent.indexWorkspace();
    }
  }

  private initializeTools(): void {
    // Microchip API Tool
    this.availableTools.set('microchip-api', {
      name: 'Microchip API',
      description: 'Direct access to Microchip AI API for general questions about microcontrollers, development tools, and products',
      keywords: ['microcontroller', 'pic', 'avr', 'sam', 'mplab', 'atmel', 'development', 'programming', 'code', 'hardware', 'datasheet', 'peripheral'],
      execute: async (query: string, _context: ConversationContext) => {
        return await this.callAPI(query);
      }
    });

    // Code Analysis Tool
    this.availableTools.set('code-analysis', {
      name: 'Code Analysis',
      description: 'Analyze code snippets, debug issues, and provide optimization suggestions',
      keywords: ['debug', 'error', 'compile', 'optimization', 'review', 'analyze', 'fix', 'bug', 'syntax', 'performance'],
      execute: async (query: string, _context: ConversationContext) => {
        const enhancedQuery = `Please analyze this code or help with this programming issue: ${query}. Provide detailed analysis, identify potential issues, and suggest improvements.`;
        return await this.callAPI(enhancedQuery);
      }
    });

    // Documentation Tool
    this.availableTools.set('documentation', {
      name: 'Documentation Assistant',
      description: 'Help find and explain documentation, datasheets, and technical specifications',
      keywords: ['documentation', 'datasheet', 'manual', 'reference', 'specification', 'guide', 'tutorial', 'example', 'api', 'register'],
      execute: async (query: string, _context: ConversationContext) => {
        const enhancedQuery = `Help me find or understand documentation: ${query}. Please provide detailed explanations and point me to relevant resources.`;
        return await this.callAPI(enhancedQuery);
      }
    });

    // Project Setup Tool
    this.availableTools.set('project-setup', {
      name: 'Project Setup Assistant',
      description: 'Help with project configuration, toolchain setup, and development environment',
      keywords: ['setup', 'configure', 'install', 'toolchain', 'environment', 'project', 'makefile', 'build', 'compiler', 'linker'],
      execute: async (query: string, _context: ConversationContext) => {
        const enhancedQuery = `Help me with project setup or configuration: ${query}. Please provide step-by-step instructions and best practices.`;
        return await this.callAPI(enhancedQuery);
      }
    });

    // Troubleshooting Tool
    this.availableTools.set('troubleshooting', {
      name: 'Troubleshooting Assistant',
      description: 'Diagnose and solve hardware and software issues',
      keywords: ['problem', 'issue', 'not working', 'failed', 'error', 'troubleshoot', 'diagnose', 'solve', 'broken', 'malfunction'],
      execute: async (query: string, _context: ConversationContext) => {
        const enhancedQuery = `Help me troubleshoot this issue: ${query}. Please provide systematic diagnostic steps and potential solutions.`;
        return await this.callAPI(enhancedQuery);
      }
    });
  }

  async testConnection(): Promise<APIResponse> {
    if (!this.apiKey) {
      return {
        success: false,
        message: '',
        error: 'API key is required'
      };
    }

    try {
      const response = await this.callAPI('Hello, are you working?');
      return {
        success: true,
        message: response
      };
    } catch (error) {
      return {
        success: false,
        message: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async sendMessage(userMessage: string): Promise<APIResponse> {
    if (!this.apiKey) {
      throw new Error('API key is required');
    }

    if (!userMessage.trim()) {
      throw new Error('Message cannot be empty');
    }

    // Add user message to conversation context
    const userChatMessage: ChatMessage = {
      id: Date.now().toString(),
      text: userMessage,
      isBot: false,
      timestamp: new Date()
    };
    this.conversationContext.history.push(userChatMessage);

    let response: string;
    let intent: string | undefined;
    let toolUsed: string | undefined;
    let confidence: number | undefined;

    if (this.advancedAgentMode && this.langChainAgent) {
      // Use advanced LangChain agent
      const agentResponse = await this.langChainAgent.processWithAdvancedAgent(userMessage);
      response = agentResponse.message;
      intent = agentResponse.intent;
      toolUsed = agentResponse.toolUsed;
      confidence = agentResponse.confidence;
    } else if (this.agentMode) {
      // Use basic agent mode with intent detection and tool routing
      const agentResponse = await this.processWithAgent(userMessage);
      response = agentResponse.message;
      intent = agentResponse.intent;
      toolUsed = agentResponse.toolUsed;
      confidence = agentResponse.confidence;
    } else {
      // Use direct API call
      response = await this.callDirectAPI(userMessage);
    }

    // Add bot response to conversation context
    const botChatMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      text: response,
      isBot: true,
      timestamp: new Date(),
      intent,
      toolUsed,
      confidence
    };
    this.conversationContext.history.push(botChatMessage);

    // Update legacy conversation history for backward compatibility
    this.conversationHistory.push(userMessage);

    // Keep only last 5 messages for context
    if (this.conversationHistory.length > 5) {
      this.conversationHistory = this.conversationHistory.slice(-5);
    }

    // Keep conversation context history manageable
    if (this.conversationContext.history.length > 20) {
      this.conversationContext.history = this.conversationContext.history.slice(-20);
    }

    return {
      success: true,
      message: response,
      intent,
      toolUsed,
      confidence
    };
  }

  private async processWithAgent(userMessage: string): Promise<APIResponse> {
    // Classify intent
    const classification = this.classifyIntent(userMessage);

    // Get the appropriate tool
    const tool = this.availableTools.get(classification.suggestedTool);

    if (!tool) {
      // Fallback to direct API if no tool found
      const response = await this.callDirectAPI(userMessage);
      return {
        success: true,
        message: response,
        intent: classification.intent,
        toolUsed: 'microchip-api',
        confidence: classification.confidence
      };
    }

    // Execute the tool
    const response = await tool.execute(userMessage, this.conversationContext);

    return {
      success: true,
      message: response,
      intent: classification.intent,
      toolUsed: tool.name,
      confidence: classification.confidence
    };
  }

  private classifyIntent(userMessage: string): IntentClassification {
    const message = userMessage.toLowerCase();
    const words = message.split(/\s+/);

    let bestMatch = {
      tool: 'microchip-api',
      score: 0,
      intent: 'general-inquiry'
    };

    // Score each tool based on keyword matches
    for (const [toolName, tool] of this.availableTools) {
      let score = 0;

      for (const keyword of tool.keywords) {
        if (message.includes(keyword.toLowerCase())) {
          score += 2; // Exact keyword match
        }

        // Check for partial matches
        for (const word of words) {
          if (word.includes(keyword.toLowerCase()) || keyword.toLowerCase().includes(word)) {
            score += 1;
          }
        }
      }

      if (score > bestMatch.score) {
        bestMatch = {
          tool: toolName,
          score,
          intent: this.getIntentFromTool(toolName)
        };
      }
    }

    // Calculate confidence based on score
    const maxPossibleScore = Math.max(...Array.from(this.availableTools.values()).map(t => t.keywords.length * 2));
    const confidence = Math.min(bestMatch.score / maxPossibleScore, 1.0);

    return {
      intent: bestMatch.intent,
      confidence,
      suggestedTool: bestMatch.tool,
      reasoning: `Matched ${bestMatch.score} keywords for ${bestMatch.tool}`
    };
  }

  private getIntentFromTool(toolName: string): string {
    const intentMap: Record<string, string> = {
      'microchip-api': 'general-inquiry',
      'code-analysis': 'code-help',
      'documentation': 'documentation-request',
      'project-setup': 'setup-assistance',
      'troubleshooting': 'problem-solving'
    };
    return intentMap[toolName] || 'general-inquiry';
  }

  private async callDirectAPI(userMessage: string): Promise<string> {
    return await this.callAPI(userMessage);
  }

  private async callAPI(userMessage: string): Promise<string> {
    const requestBody = {
      questions: [userMessage],
      answers: this.conversationHistory.slice(-5),
      category: 101,
      logQnA: true,
      client: "react-chatbot",
      apiKey: this.apiKey
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      // Use Node.js compatible HTTP request
      const response = await this.makeHttpRequest(`${this.baseURL}${this.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const data = await response.json() as {
        success?: boolean;
        message?: string;
        error?: string;
      };

      if (!response.ok) {
        if (data.error) {
          throw new Error(data.error);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      if (data.success && data.message) {
        return data.message;
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Unexpected response format from server');
      }

    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout - Server took too long to respond');
        } else if (error.message.includes('Failed to fetch')) {
          throw new Error('Network error - Please check if the backend server is running');
        } else {
          throw error;
        }
      } else {
        throw new Error('An unexpected error occurred');
      }
    }
  }
}

// Create a singleton instance
export const microchipAPI = new MicrochipAPI();
