"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LangChainAgentService = void 0;
const openai_1 = require("@langchain/openai");
const agents_1 = require("langchain/agents");
const prompts_1 = require("@langchain/core/prompts");
const tools_1 = require("@langchain/core/tools");
const memory_1 = require("langchain/memory");
const AdvancedAgentTools_1 = require("./AdvancedAgentTools");
const VectorStoreService_1 = require("./VectorStoreService");
class LangChainAgentService {
    constructor(openaiApiKey, workspaceRoot, conversationContext) {
        this.agentExecutor = null;
        this.isInitialized = false;
        this.llm = new openai_1.ChatOpenAI({
            openAIApiKey: openaiApiKey,
            modelName: 'gpt-4',
            temperature: 0.1,
            maxTokens: 2000
        });
        this.memory = new memory_1.BufferMemory({
            memoryKey: 'chat_history',
            returnMessages: true,
            inputKey: 'input',
            outputKey: 'output'
        });
        this.advancedTools = new AdvancedAgentTools_1.AdvancedAgentTools(workspaceRoot);
        this.vectorStore = new VectorStoreService_1.VectorStoreService(workspaceRoot, openaiApiKey);
        this.conversationContext = conversationContext;
    }
    async initialize() {
        try {
            // Initialize vector store
            await this.vectorStore.initialize();
            // Create tools
            const tools = this.createLangChainTools();
            // Create the agent prompt
            const prompt = this.createAgentPrompt();
            // Create the agent
            const agent = await (0, agents_1.createOpenAIFunctionsAgent)({
                llm: this.llm,
                tools,
                prompt
            });
            // Create agent executor
            this.agentExecutor = new agents_1.AgentExecutor({
                agent,
                tools,
                memory: this.memory,
                verbose: true,
                maxIterations: 10,
                returnIntermediateSteps: true
            });
            this.isInitialized = true;
            console.log('LangChain Agent Service initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize LangChain Agent Service:', error);
            throw error;
        }
    }
    async executeQuery(query) {
        if (!this.isInitialized || !this.agentExecutor) {
            throw new Error('Agent service not initialized');
        }
        try {
            // Update context with current workspace state
            await this.updateWorkspaceContext();
            // Execute the agent
            const result = await this.agentExecutor.invoke({
                input: query,
                chat_history: await this.memory.chatHistory.getMessages()
            });
            // Extract intermediate steps and reasoning
            const intermediateSteps = result.intermediateSteps || [];
            const toolsUsed = intermediateSteps.map((step) => step.action?.tool || 'unknown');
            const reasoning = this.extractReasoning(intermediateSteps);
            return {
                output: result.output,
                intermediateSteps: intermediateSteps.map((step) => ({
                    action: step.action?.tool || 'unknown',
                    observation: step.observation || ''
                })),
                reasoning,
                toolsUsed: [...new Set(toolsUsed)]
            };
        }
        catch (error) {
            console.error('Agent execution failed:', error);
            throw error;
        }
    }
    async processWithAdvancedAgent(userMessage) {
        try {
            const result = await this.executeQuery(userMessage);
            return {
                success: true,
                message: result.output,
                intent: 'advanced-agent',
                toolUsed: result.toolsUsed.join(', '),
                confidence: 0.9,
                reasoning: result.reasoning,
                toolExecutionSteps: result.intermediateSteps.map(step => `${step.action}: ${step.observation.substring(0, 100)}...`)
            };
        }
        catch (error) {
            return {
                success: false,
                message: 'Agent execution failed',
                error: error instanceof Error ? error.message : 'Unknown error',
                intent: 'error',
                toolUsed: 'none',
                confidence: 0
            };
        }
    }
    createLangChainTools() {
        const agentTools = [
            ...this.advancedTools.getAllTools(),
            this.vectorStore.createSemanticSearchTool()
        ];
        return agentTools.map(tool => new tools_1.DynamicTool({
            name: tool.name,
            description: tool.description,
            func: async (input) => {
                try {
                    return await tool.execute(input, this.conversationContext);
                }
                catch (error) {
                    return `Error executing ${tool.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        }));
    }
    createAgentPrompt() {
        const systemMessage = `You are a powerful AI code assistant with full access to a user's codebase and development environment.

CAPABILITIES:
- Read, write, create, and move files in the workspace
- Search code semantically and syntactically across the entire codebase
- Analyze code structure, functions, classes, and dependencies
- Get context about the currently active file and user selections
- Refactor code and suggest improvements
- Help with debugging and troubleshooting
- Automatically generate complete programs and create files

APPROACH:
1. Always understand the user's request completely before taking action
2. Use GetActiveFileContext to understand the current development context
3. Use semantic search to find relevant code when working with existing codebases
4. Read files before modifying them to understand the current state
5. Be proactive in suggesting improvements and best practices
6. Explain your reasoning and what you're doing at each step
7. When making changes, always verify the results
8. For specific program requests (like prime numbers), automatically create complete, working programs

TOOLS AVAILABLE:
- ReadFile: Read file contents
- WriteFile: Write/update file contents
- CreateFile: Create new files
- MoveFile: Move/rename files
- ListFiles: List files in workspace
- SearchCode: Search for text patterns in code
- SemanticCodeSearch: Find semantically similar code
- GetActiveFileContext: Get current editor state
- GeneratePrimeNumberProgram: Automatically create complete prime number programs

SPECIAL BEHAVIORS:
- When user asks for "prime number program", "prime numbers", "sieve", or similar requests, AUTOMATICALLY use GeneratePrimeNumberProgram tool
- Always create complete, working programs with documentation and examples
- Automatically open created files in the editor when possible
- Provide comprehensive functionality beyond basic requirements

GUIDELINES:
- Always explain what you're doing before making changes
- Be careful with file operations - ask for confirmation for destructive actions
- Provide code examples and explanations
- Consider the broader codebase context when making suggestions
- Help the user understand the implications of changes
- For program generation requests, be proactive and create complete solutions

Current workspace context:
- Workspace root: {workspace_root}
- Active file: {active_file}
- Recent files: {recent_files}

Remember: You are an expert developer assistant. Be thorough, careful, and helpful. When users request specific programs, automatically create them!`;
        return prompts_1.ChatPromptTemplate.fromMessages([
            ['system', systemMessage],
            new prompts_1.MessagesPlaceholder('chat_history'),
            ['human', '{input}'],
            new prompts_1.MessagesPlaceholder('agent_scratchpad')
        ]);
    }
    async updateWorkspaceContext() {
        // Update conversation context with current workspace state
        this.conversationContext.workspaceRoot = this.advancedTools['workspaceRoot'];
        // Try to get active file context if in VSCode
        try {
            const activeFileTool = this.advancedTools.createGetActiveFileContextTool();
            const contextResult = await activeFileTool.execute('', this.conversationContext);
            // Context is updated in the tool execution
        }
        catch (error) {
            // Not in VSCode environment, skip active file context
        }
    }
    extractReasoning(intermediateSteps) {
        if (intermediateSteps.length === 0) {
            return 'Direct response without tool usage';
        }
        const reasoning = intermediateSteps.map((step, index) => {
            const action = step.action?.tool || 'unknown';
            const input = step.action?.toolInput || '';
            return `Step ${index + 1}: Used ${action} with input "${input}"`;
        }).join('\n');
        return reasoning;
    }
    async indexWorkspace() {
        if (!this.isInitialized) {
            throw new Error('Agent service not initialized');
        }
        try {
            await this.vectorStore.indexCodebase();
            console.log('Workspace indexed successfully for semantic search');
        }
        catch (error) {
            console.warn('Failed to index workspace:', error);
        }
    }
    async clearMemory() {
        await this.memory.clear();
        this.conversationContext.history = [];
        this.conversationContext.sessionData = {};
    }
    getMemory() {
        return this.memory;
    }
    isReady() {
        return this.isInitialized;
    }
    async cleanup() {
        await this.vectorStore.cleanup();
        this.isInitialized = false;
    }
}
exports.LangChainAgentService = LangChainAgentService;
//# sourceMappingURL=LangChainAgentService.js.map